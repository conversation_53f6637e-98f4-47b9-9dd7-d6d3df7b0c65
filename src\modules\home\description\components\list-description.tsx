import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { useGetHomeBase } from '../../queries/get-home-base';

const DescriptionSection: React.FC = () => {
  const { data } = useGetHomeBase();
  const desc = data?.data || '';
  const description = desc?.description || '';

  if (!description) {
    return (
      <div className="p-6">
        <p className="mb-6">No description section found.</p>
        <Link href="/home/<USER>/edit">
          <Button>Create Description Section</Button>
        </Link>
      </div>
    );
  }

  return (
    <section className="p-6">
      <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Description Section</h1>
          <Link href="/home/<USER>/edit">
            <Button>Edit</Button>
          </Link>
        </div>
        <div className="bg-white rounded-lg shadow">
          <table className="min-w-full text-left border-separate border-spacing-0">
            <thead>
              <tr className="bg-gray-100">
                <th className="border px-4 py-2">SN</th>
                <th className="border py-3 px-4">Description</th>
                <th className="border py-3 px-4">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-t last:border-b hover:bg-gray-100 transition">
                <td className="border py-2 px-4">1</td>
                <td className="border py-2 px-4 max-w-xs truncate">
                  {description}
                </td>
                <td className="py-2 px-4">
                  <Link
                    href="/home/<USER>/edit"
                    className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                  >
                    Edit
                  </Link>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
  );
};

export default DescriptionSection;
